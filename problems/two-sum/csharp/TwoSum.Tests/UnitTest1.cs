using System;
using Xunit;

namespace TwoSum.Tests
{
    public class SolutionTests
    {
        private readonly Solution _solution;

        public SolutionTests()
        {
            _solution = new Solution();
        }

        [Fact]
        public void TwoSum_Example1_ReturnsCorrectIndices()
        {
            // Arrange
            int[] nums = {2, 7, 11, 15};
            int target = 9;
            int[] expected = {0, 1};

            // Act
            int[] result = _solution.TwoSum(nums, target);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void TwoSum_Example2_ReturnsCorrectIndices()
        {
            // Arrange
            int[] nums = {3, 2, 4};
            int target = 6;
            int[] expected = {1, 2};

            // Act
            int[] result = _solution.TwoSum(nums, target);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void TwoSum_Example3_ReturnsCorrectIndices()
        {
            // Arrange
            int[] nums = {3, 3};
            int target = 6;
            int[] expected = {0, 1};

            // Act
            int[] result = _solution.TwoSum(nums, target);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void TwoSum_NegativeNumbers_ReturnsCorrectIndices()
        {
            // Arrange
            int[] nums = {-1, -2, -3, -4, -5};
            int target = -8;
            int[] expected = {2, 4}; // -3 + -5 = -8

            // Act
            int[] result = _solution.TwoSum(nums, target);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void TwoSum_MixedNumbers_ReturnsCorrectIndices()
        {
            // Arrange
            int[] nums = {-3, 4, 3, 90};
            int target = 0;
            int[] expected = {0, 2}; // -3 + 3 = 0

            // Act
            int[] result = _solution.TwoSum(nums, target);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void TwoSum_NullArray_ThrowsArgumentException()
        {
            // Arrange
            int[] nums = null;
            int target = 9;

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _solution.TwoSum(nums, target));
        }

        [Fact]
        public void TwoSum_ArrayWithOneElement_ThrowsArgumentException()
        {
            // Arrange
            int[] nums = {5};
            int target = 5;

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _solution.TwoSum(nums, target));
        }

        [Fact]
        public void TwoSum_NoSolutionExists_ThrowsInvalidOperationException()
        {
            // Arrange
            int[] nums = {1, 2, 3, 4};
            int target = 10;

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => _solution.TwoSum(nums, target));
        }

        [Fact]
        public void TwoSumBruteForce_Example1_ReturnsCorrectIndices()
        {
            // Arrange
            int[] nums = {2, 7, 11, 15};
            int target = 9;
            int[] expected = {0, 1};

            // Act
            int[] result = _solution.TwoSumBruteForce(nums, target);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void TwoSumBruteForce_NullArray_ThrowsArgumentException()
        {
            // Arrange
            int[] nums = null;
            int target = 9;

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _solution.TwoSumBruteForce(nums, target));
        }
    }
}
